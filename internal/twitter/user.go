package twitter

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
	"github.com/unkmonster/tmd/internal/utils"
)

// 定义时间线API接口
type TimelineAPI interface {
	SetCursor(cursor string)
	Path() string
	QueryParam() url.Values
}

// 定义推文解析器接口
type TweetParser interface {
	ParseTweet(tweetResults *gjson.Result) *Tweet
	ParseItemContents(itemContents []gjson.Result) []*Tweet
}

type FollowState int

const (
	FS_UNFOLLOW FollowState = iota
	FS_FOLLOWING
	FS_REQUESTED
)

type User struct {
	Id           uint64
	Name         string
	ScreenName   string
	IsProtected  bool
	FriendsCount int
	Followstate  FollowState
	MediaCount   int
	StatusesCount int  // 总推文数
	Muting       bool
	Blocking     bool
}

func GetUserById(ctx context.Context, client *resty.Client, id uint64) (*User, error) {
	api := userByRestId{id}
	getUrl := makeUrl(&api)
	r, err := getUser(ctx, client, getUrl)
	if err != nil {
		return nil, fmt.Errorf("failed to get user [%d]: %v", id, err)
	}
	return r, err
}

func GetUserByScreenName(ctx context.Context, client *resty.Client, screenName string) (*User, error) {
	u := makeUrl(&userByScreenName{screenName: screenName})
	r, err := getUser(ctx, client, u)
	if err != nil {
		return nil, fmt.Errorf("failed to get user [%s]: %v", screenName, err)
	}
	return r, err
}

func getUser(ctx context.Context, client *resty.Client, url string) (*User, error) {
	resp, err := client.R().SetContext(ctx).Get(url)
	if err != nil {
		return nil, err
	}
	return parseRespJson(resp.Body())
}

func parseUserResults(user_results *gjson.Result) (*User, error) {
	result := user_results.Get("result")
	if result.Get("__typename").String() == "UserUnavailable" {
		return nil, fmt.Errorf("user unavaiable")
	}
	legacy := result.Get("legacy")

	restId := result.Get("rest_id")
	friends_count := legacy.Get("friends_count")
	name := legacy.Get("name")
	screen_name := legacy.Get("screen_name")
	protected := legacy.Get("protected").Exists() && legacy.Get("protected").Bool()
	media_count := legacy.Get("media_count")
	statuses_count := legacy.Get("statuses_count")
	muting := legacy.Get("muting")
	blocking := legacy.Get("blocking")

	usr := User{}
	if foll := legacy.Get("following"); foll.Exists() {
		if foll.Bool() {
			usr.Followstate = FS_FOLLOWING
		} else {
			usr.Followstate = FS_UNFOLLOW
		}
	} else if legacy.Get("follow_request_sent").Exists() {
		usr.Followstate = FS_REQUESTED
	} else {
		usr.Followstate = FS_UNFOLLOW
	}

	usr.FriendsCount = int(friends_count.Int())
	usr.Id = restId.Uint()
	usr.IsProtected = protected
	usr.Name = name.String()
	usr.ScreenName = screen_name.String()
	usr.MediaCount = int(media_count.Int())
	usr.StatusesCount = int(statuses_count.Int())
	usr.Muting = muting.Exists() && muting.Bool()
	usr.Blocking = blocking.Exists() && blocking.Bool()
	return &usr, nil
}

func parseRespJson(resp []byte) (*User, error) {
	user := gjson.GetBytes(resp, "data.user")
	if !user.Exists() {
		return nil, fmt.Errorf("user does not exist")
	}
	return parseUserResults(&user)
}

func (u *User) IsVisiable() bool {
	return u.Followstate == FS_FOLLOWING || !u.IsProtected
}

func itemContentsToTweets(itemContents []gjson.Result) []*Tweet {
	res := make([]*Tweet, 0, len(itemContents))
	for _, itemContent := range itemContents {
		tweetResults := getResults(itemContent, timelineTweet)
		if tw := parseTweetResults(&tweetResults); tw != nil {
			res = append(res, tw)
		}
	}
	return res
}

// 媒体推文解析器
type MediaTweetParser struct{}

func (p MediaTweetParser) ParseTweet(tweetResults *gjson.Result) *Tweet {
	return parseTweetResults(tweetResults)
}

func (p MediaTweetParser) ParseItemContents(itemContents []gjson.Result) []*Tweet {
	res := make([]*Tweet, 0, len(itemContents))
	for _, itemContent := range itemContents {
		tweetResults := getResults(itemContent, timelineTweet)
		if tw := p.ParseTweet(&tweetResults); tw != nil {
			res = append(res, tw)
		}
	}
	return res
}

// 全部推文解析器（目前逻辑相同，但可以扩展）
type AllTweetParser struct{}

func (p AllTweetParser) ParseTweet(tweetResults *gjson.Result) *Tweet {
	return parseTweetResults(tweetResults) // 目前相同，但可以扩展
}

func (p AllTweetParser) ParseItemContents(itemContents []gjson.Result) []*Tweet {
	res := make([]*Tweet, 0, len(itemContents))
	for _, itemContent := range itemContents {
		tweetResults := getResults(itemContent, timelineTweet)
		if tw := p.ParseTweet(&tweetResults); tw != nil {
			res = append(res, tw)
		}
	}
	return res
}

// 统一的获取推文方法
func (u *User) getTweets(ctx context.Context, client *resty.Client, api TimelineAPI, parser TweetParser, timeRange *utils.TimeRange) ([]*Tweet, error) {
	if !u.IsVisiable() {
		return nil, nil
	}

	results := make([]*Tweet, 0)

	var minTime *time.Time
	var maxTime *time.Time

	if timeRange != nil {
		minTime = &timeRange.Min
		maxTime = &timeRange.Max
	}

	pageNum := 1
	for {
		currentTweets, next, err := u.getTweetsOnePage(ctx, api, client, parser)
		if err != nil {
			return nil, err
		}

		if len(currentTweets) == 0 {
			fmt.Printf("[%s] 第%d页为空，获取结束\n", time.Now().Format("15:04:05"), pageNum)
			break // empty page
		}

		fmt.Printf("[%s] 第%d页获取到%d条推文\n", time.Now().Format("15:04:05"), pageNum, len(currentTweets))
		pageNum++

		if next == "" {
			fmt.Printf("[%s] 下一页cursor为空，获取结束\n", time.Now().Format("15:04:05"))
			break
		}

		api.SetCursor(next)

		if timeRange == nil {
			results = append(results, currentTweets...)
			fmt.Printf("[%s] 无时间范围限制，累计获取%d条推文\n", time.Now().Format("15:04:05"), len(results))
			continue
		}

		// 筛选推文，并判断是否获取下页
		cutMin, cutMax, currentTweets := filterTweetsByTimeRange(currentTweets, minTime, maxTime)
		results = append(results, currentTweets...)

		fmt.Printf("[%s] 时间筛选后保留%d条推文，累计%d条，cutMin=%v, cutMax=%v\n",
			time.Now().Format("15:04:05"),
			len(currentTweets),
			len(results),
			cutMin,
			cutMax)

		if cutMin {
			fmt.Printf("[%s] 遇到时间截断，停止获取\n", time.Now().Format("15:04:05"))
			break
		}
		if cutMax && len(currentTweets) != 0 {
			maxTime = nil
		}
	}

	fmt.Printf("[%s] 推文获取完成，总计获取%d条推文\n", time.Now().Format("15:04:05"), len(results))
	if len(results) > 0 {
		fmt.Printf("[%s] 时间范围: %s 到 %s\n",
			time.Now().Format("15:04:05"),
			results[len(results)-1].CreatedAt.Format("2006-01-02 15:04:05"),
			results[0].CreatedAt.Format("2006-01-02 15:04:05"))
	}

	return results, nil
}

// 统一的获取单页推文方法
func (u *User) getTweetsOnePage(ctx context.Context, api TimelineAPI, client *resty.Client, parser TweetParser) ([]*Tweet, string, error) {
	if !u.IsVisiable() {
		return nil, "", nil
	}

	itemContents, next, err := getTimelineItemContents(ctx, api, client, "data.user.result.timeline_v2.timeline.instructions")
	return parser.ParseItemContents(itemContents), next, err
}

func (u *User) getMediasOnePage(ctx context.Context, api *userMedia, client *resty.Client) ([]*Tweet, string, error) {
	if !u.IsVisiable() {
		return nil, "", nil
	}

	itemContents, next, err := getTimelineItemContents(ctx, api, client, "data.user.result.timeline_v2.timeline.instructions")
	return itemContentsToTweets(itemContents), next, err
}

// 在逆序切片中，筛选出在 timerange 范围内的推文
func filterTweetsByTimeRange(tweets []*Tweet, min *time.Time, max *time.Time) (cutMin bool, cutMax bool, res []*Tweet) {
	n := len(tweets)
	begin, end := 0, n

	// 从左到右查找第一个小于 min 的推文
	if min != nil && !min.IsZero() {
		for i := 0; i < n; i++ {
			if !tweets[i].CreatedAt.After(*min) {
				end = i // 找到第一个不大于 min 的推文位置
				cutMin = true
				break
			}
		}
	}

	// 从右到左查找最后一个大于 max 的推文
	if max != nil && !max.IsZero() {
		for i := n - 1; i >= 0; i-- {
			if !tweets[i].CreatedAt.Before(*max) {
				begin = i + 1 // 找到第一个不小于 max 的推文位置
				cutMax = true
				break
			}
		}
	}

	if begin >= end {
		// 如果最终的范围无效，返回空结果
		return cutMin, cutMax, nil
	}

	res = tweets[begin:end]
	return
}

func (u *User) GetMeidas(ctx context.Context, client *resty.Client, timeRange *utils.TimeRange) ([]*Tweet, error) {
	api := &userMedia{
		userId: u.Id,
		count:  100,
		cursor: "",
	}
	parser := MediaTweetParser{}
	return u.getTweets(ctx, client, api, parser, timeRange)
}

// 获取用户所有推文（包括非媒体推文）
func (u *User) GetAllTweets(ctx context.Context, client *resty.Client, timeRange *utils.TimeRange) ([]*Tweet, error) {
	api := &userTweets{
		userId: u.Id,
		count:  100,
		cursor: "",
	}
	parser := AllTweetParser{}
	return u.getTweets(ctx, client, api, parser, timeRange)
}

func (u *User) Title() string {
	return fmt.Sprintf("%s(%s)", u.Name, u.ScreenName)
}

func (u *User) Following() UserFollowing {
	return UserFollowing{u}
}

func FollowUser(ctx context.Context, client *resty.Client, user *User) error {
	url := "https://x.com/i/api/1.1/friendships/create.json"
	_, err := client.R().SetFormData(map[string]string{
		"user_id": fmt.Sprintf("%d", user.Id),
		// "skip_status":                       1,
		// "include_profile_interstitial_type": 1,
		// "include_blocking":                  1,
		// "include_blocked_by":                1,
		// "include_followed_by":               1,
		// "include_want_retweets":             1,
		// "include_mute_edge":                 1,
		// "include_can_dm":                    1,
		// "include_can_media_tag":             1,
		// "include_ext_is_blue_verified":      1,
		// "include_ext_verified_type":         1,
		// "include_ext_profile_image_shape":   1,
	}).SetContext(ctx).Post(url)
	return err
}
